﻿// <auto-generated />
using System;
using Auth.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Auth.Domain.Migrations
{
    [DbContext(typeof(DocAIAuthContext))]
    [Migration("20250719075636_migration1")]
    partial class migration1
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Auth.Domain.Models.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Departments");

                    b.HasData(
                        new
                        {
                            Id = new Guid("8bf13891-1ce9-405c-add9-0ada93308671"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 503, DateTimeKind.Utc).AddTicks(5751),
                            Description = "DepartentA",
                            Name = "DepartentA",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 503, DateTimeKind.Utc).AddTicks(5861)
                        },
                        new
                        {
                            Id = new Guid("d8854d21-8fae-46aa-b51b-0de060b92ee3"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 503, DateTimeKind.Utc).AddTicks(6064),
                            Description = "Company",
                            Name = "Company",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 503, DateTimeKind.Utc).AddTicks(6065)
                        });
                });

            modelBuilder.Entity("Auth.Domain.Models.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Permissions");

                    b.HasData(
                        new
                        {
                            Id = new Guid("3796cdb0-7c0a-4cc6-a757-883fe1865fb6"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(3401),
                            Description = "Quyền xem mọi tài liệu trong hệ thống ",
                            Name = "VIEW_ANY_DOCUMENT",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(3502)
                        },
                        new
                        {
                            Id = new Guid("e72214a0-24bc-471a-aca5-d897f4da0aad"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(3672),
                            Description = "Quyền xem tài liệu thuộc phòng ban của mình.",
                            Name = "VIEW_OWN_DEPARTMENT_DOCUMENT",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(3674)
                        },
                        new
                        {
                            Id = new Guid("febebe25-dd94-4ba1-bdbd-810e4503bccd"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(3676),
                            Description = "Quyền xem tài liệu của mình.",
                            Name = "VIEW_DEPARTMENT_DOCUMENT",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(3676)
                        });
                });

            modelBuilder.Entity("Auth.Domain.Models.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            Id = new Guid("4e29a870-9131-4cc2-97ca-eaa748b5f17f"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(5979),
                            Description = "Employee",
                            RoleName = "Employee",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(6068)
                        },
                        new
                        {
                            Id = new Guid("a996692c-1f5e-4458-8dcf-c2494a47b6d6"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(6146),
                            Description = "Admin",
                            RoleName = "Admin",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(6146)
                        },
                        new
                        {
                            Id = new Guid("a5ddf431-aae9-4d9f-8d61-1a37776bb4bb"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(6148),
                            Description = "Manager",
                            RoleName = "Manager",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(6148)
                        },
                        new
                        {
                            Id = new Guid("8e7d55e4-67d3-4b73-9995-21b163493136"),
                            CreateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(6150),
                            Description = "Editor",
                            RoleName = "Editor",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 504, DateTimeKind.Utc).AddTicks(6150)
                        });
                });

            modelBuilder.Entity("Auth.Domain.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Active")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("RoleId");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = new Guid("13d466ed-8a2d-414d-88c0-9c7adcac2616"),
                            Active = false,
                            CreatAt = new DateTime(2025, 7, 19, 7, 56, 35, 529, DateTimeKind.Utc).AddTicks(9353),
                            DepartmentId = new Guid("d8854d21-8fae-46aa-b51b-0de060b92ee3"),
                            Email = "<EMAIL>",
                            FullName = "Admin",
                            Password = "TuCboI2USCS1QIyw6VyWc8tCL2THhdytgapBAeJ3JZLExF6ZwuT4C9Ed0U1GMkIN",
                            Phone = "0847911068",
                            RoleId = new Guid("a996692c-1f5e-4458-8dcf-c2494a47b6d6"),
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 529, DateTimeKind.Utc).AddTicks(9468)
                        },
                        new
                        {
                            Id = new Guid("595dd357-aaec-455e-9fa7-4fc88d4b819c"),
                            Active = false,
                            CreatAt = new DateTime(2025, 7, 19, 7, 56, 35, 549, DateTimeKind.Utc).AddTicks(7435),
                            DepartmentId = new Guid("8bf13891-1ce9-405c-add9-0ada93308671"),
                            Email = "<EMAIL>",
                            FullName = "Manager",
                            Password = "3M8X+PhlXm7jFY2AnRf4vN+DlkNfkHDdob+favJqvElQ3Wu8yc5E8QK9XgROx1zD",
                            Phone = "0123456789",
                            RoleId = new Guid("a5ddf431-aae9-4d9f-8d61-1a37776bb4bb"),
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 549, DateTimeKind.Utc).AddTicks(7440)
                        },
                        new
                        {
                            Id = new Guid("fd05266c-baf5-49bb-a846-554461bcc411"),
                            Active = false,
                            CreatAt = new DateTime(2025, 7, 19, 7, 56, 35, 570, DateTimeKind.Utc).AddTicks(1797),
                            DepartmentId = new Guid("8bf13891-1ce9-405c-add9-0ada93308671"),
                            Email = "<EMAIL>",
                            FullName = "Employee",
                            Password = "S4PnHpqjUYW10IHpMqCr6suaVDHTNW09huYZhD4nZn9AOIGbRFQcJ0EKAnD4GImD",
                            Phone = "0123456789",
                            RoleId = new Guid("4e29a870-9131-4cc2-97ca-eaa748b5f17f"),
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 570, DateTimeKind.Utc).AddTicks(1801)
                        },
                        new
                        {
                            Id = new Guid("5c49c1cb-719e-42eb-8028-f2eb3eaea4cd"),
                            Active = false,
                            CreatAt = new DateTime(2025, 7, 19, 7, 56, 35, 590, DateTimeKind.Utc).AddTicks(5804),
                            DepartmentId = new Guid("8bf13891-1ce9-405c-add9-0ada93308671"),
                            Email = "<EMAIL>",
                            FullName = "Editor",
                            Password = "8LYWgc6vmJw/N1dWFw6Kme0xHlScHmMEXXKC2ZjS6BhNzUnKV17vqjJhLizxSs52",
                            Phone = "0123456789",
                            RoleId = new Guid("8e7d55e4-67d3-4b73-9995-21b163493136"),
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 590, DateTimeKind.Utc).AddTicks(5810)
                        });
                });

            modelBuilder.Entity("Auth.Domain.Models.UserPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserPermissions");

                    b.HasData(
                        new
                        {
                            Id = new Guid("68f0ed30-8378-4c7a-9ac1-7e59efd23f7d"),
                            PermissionId = new Guid("3796cdb0-7c0a-4cc6-a757-883fe1865fb6"),
                            UserId = new Guid("13d466ed-8a2d-414d-88c0-9c7adcac2616")
                        },
                        new
                        {
                            Id = new Guid("b1ebed91-c7b3-41cd-9364-dbff90079fbc"),
                            PermissionId = new Guid("e72214a0-24bc-471a-aca5-d897f4da0aad"),
                            UserId = new Guid("595dd357-aaec-455e-9fa7-4fc88d4b819c")
                        },
                        new
                        {
                            Id = new Guid("0c56263b-b1f0-43d2-a701-a6d66e9c2b4f"),
                            PermissionId = new Guid("febebe25-dd94-4ba1-bdbd-810e4503bccd"),
                            UserId = new Guid("fd05266c-baf5-49bb-a846-554461bcc411")
                        },
                        new
                        {
                            Id = new Guid("42f53500-74bb-4883-b397-6a511ccab4c6"),
                            PermissionId = new Guid("febebe25-dd94-4ba1-bdbd-810e4503bccd"),
                            UserId = new Guid("5c49c1cb-719e-42eb-8028-f2eb3eaea4cd")
                        },
                        new
                        {
                            Id = new Guid("b00f2a64-ed45-40fa-9941-cee61140529d"),
                            PermissionId = new Guid("febebe25-dd94-4ba1-bdbd-810e4503bccd"),
                            UserId = new Guid("595dd357-aaec-455e-9fa7-4fc88d4b819c")
                        });
                });

            modelBuilder.Entity("Auth.Domain.Models.UserSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("NotificationsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("TwoFactorMethod")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserSettings");

                    b.HasData(
                        new
                        {
                            Id = new Guid("ddfcbea3-56e9-4187-97f6-521ca24c2412"),
                            NotificationsEnabled = true,
                            TwoFactorEnabled = false,
                            TwoFactorMethod = "email",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 591, DateTimeKind.Utc).AddTicks(3274),
                            UserId = new Guid("13d466ed-8a2d-414d-88c0-9c7adcac2616")
                        },
                        new
                        {
                            Id = new Guid("dd9105eb-4df0-4c32-bc55-fd0169e386fc"),
                            NotificationsEnabled = true,
                            TwoFactorEnabled = false,
                            TwoFactorMethod = "email",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 591, DateTimeKind.Utc).AddTicks(3469),
                            UserId = new Guid("595dd357-aaec-455e-9fa7-4fc88d4b819c")
                        },
                        new
                        {
                            Id = new Guid("86254802-1d1e-4734-a25b-ef22ff39cefc"),
                            NotificationsEnabled = true,
                            TwoFactorEnabled = false,
                            TwoFactorMethod = "email",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 591, DateTimeKind.Utc).AddTicks(3471),
                            UserId = new Guid("fd05266c-baf5-49bb-a846-554461bcc411")
                        },
                        new
                        {
                            Id = new Guid("4e8bff21-b470-4b9e-92da-400d21992f96"),
                            NotificationsEnabled = true,
                            TwoFactorEnabled = false,
                            TwoFactorMethod = "email",
                            UpdateAt = new DateTime(2025, 7, 19, 7, 56, 35, 591, DateTimeKind.Utc).AddTicks(3474),
                            UserId = new Guid("5c49c1cb-719e-42eb-8028-f2eb3eaea4cd")
                        });
                });

            modelBuilder.Entity("Auth.Domain.Models.User", b =>
                {
                    b.HasOne("Auth.Domain.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auth.Domain.Models.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Auth.Domain.Models.UserPermission", b =>
                {
                    b.HasOne("Auth.Domain.Models.Permission", "Permission")
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auth.Domain.Models.User", "User")
                        .WithMany("UserPermissions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Auth.Domain.Models.UserSetting", b =>
                {
                    b.HasOne("Auth.Domain.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Auth.Domain.Models.User", b =>
                {
                    b.Navigation("UserPermissions");
                });
#pragma warning restore 612, 618
        }
    }
}
