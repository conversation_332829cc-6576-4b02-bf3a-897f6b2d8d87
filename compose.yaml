﻿services:
  # Auth API - Instance 1
  auth.api1:
    build:
     context: .
     dockerfile: Services/Auth/Auth.API/Dockerfile
    # image: magicflexing/docai-auth-api:latest
    container_name: auth.api1
    ports:
      - "5001:5001"
    environment:
      - HOSTNAME=auth-instance-1
    restart: always
    networks:
      - docai_network

  # Auth API - Instance 2
  auth.api2:
    build:
     context: .
     dockerfile: Services/Auth/Auth.API/Dockerfile
    # image: magicflexing/docai-auth-api:latest
    container_name: auth.api2
    ports:
      - "5011:5001"
    environment:
      - HOSTNAME=auth-instance-2
    restart: always
    networks:
      - docai_network

  # Document API - Instance 1
  document.api1:
    build:
     context: .
     dockerfile: Services/Document/Document.API/Dockerfile
    # image: magicflexing/docai-document-api:latest
    container_name: document.api1
    ports:
      - "5002:5002"
    environment:
      - HOSTNAME=document-instance-1
    restart: always
    networks:
      - docai_network

  # Document API - Instance 2
  document.api2:
    build:
     context: .
     dockerfile: Services/Document/Document.API/Dockerfile
    # image: magicflexing/docai-document-api:latest
    container_name: document.api2
    ports:
      - "5012:5002"
    environment:
      - HOSTNAME=document-instance-2
    restart: always
    networks:
      - docai_network

  # ChatBox API - Instance 1
  chatbox.api1:
    build:
     context: .
     dockerfile: Services/ChatBox/ChatBox.API/Dockerfile
    # image: magicflexing/docai-chatbox-api:latest
    container_name: chatbox.api1
    ports:
      - "5005:5005"
    environment:
      - HOSTNAME=chatbox-instance-1
    restart: always
    networks:
      - docai_network

  # ChatBox API - Instance 2
  chatbox.api2:
    build:
     context: .
     dockerfile: Services/ChatBox/ChatBox.API/Dockerfile
    # image: magicflexing/docai-chatbox-api:latest
    container_name: chatbox.api2
    ports:
      - "5015:5005"
    environment:
      - HOSTNAME=chatbox-instance-2
    restart: always
    networks:
      - docai_network

  # API Gateway 1
  apigateway:
    build:
     context: .
     dockerfile: ApiGateway/Dockerfile
    # image: magicflexing/docai-gateway:latest
    container_name: apigateway
    ports:
      - "5000:5000"
    restart: always
    depends_on:
      - auth.api1
      - auth.api2
      - document.api1
      - document.api2
      - notification.api1
      - notification.api2
      - chatbox.api1
      - chatbox.api2
    networks:
      - docai_network


  # Notification API - Instance 1
  notification.api1:
    build:
     context: .
     dockerfile: Services/Notification/Notification.API/Dockerfile
    # image: magicflexing/docai-notification-api:latest
    container_name: notification.api1
    ports:
      - "5004:5004"
    environment:
      - HOSTNAME=notification-instance-1
    restart: always
    networks:
      - docai_network

  # Notification API - Instance 2
  notification.api2:
    build:
     context: .
     dockerfile: Services/Notification/Notification.API/Dockerfile
    # image: magicflexing/docai-notification-api:latest
    container_name: notification.api2
    ports:
      - "5014:5004"
    environment:
      - HOSTNAME=notification-instance-2
    restart: always
    networks:
      - docai_network

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASSWORD: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - docai_network
    restart: always
    
  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - docai_network
    restart: always

  # ollama:
  #   image: ollama/ollama:latest
  #   container_name: ollama
  #   ports:
  #     - "11434:11434"
  #   volumes:
  #     - ollama_data:/root/.ollama
  #   networks:
  #     - docai_network
  #   restart: always

volumes:
  rabbitmq_data:
    driver: local
  redis_data:
    driver: local
  # ollama_data:
  #   driver: local

networks:
  docai_network:
    driver: bridge
