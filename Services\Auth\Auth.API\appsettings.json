{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=DocAIAuthData;Username=postgres;Password=**************", "Redis": "redis:6379,abortConnect=false"}, "RabbitMQ": {"Host": "rabbitmq", "Username": "guest", "Password": "guest"}, "Jwt": {"Secret": "DocAISystemSecretKeyForJWTTokenProNhuVayDoTaoDoThangNaoLayDuocKeyCuaTaoDo", "Issuer": "DocAI"}, "Email": {"SmtpServer": "smtp.gmail.com", "Port": "587", "Username": "<EMAIL>", "Password": "anuk yrxy ksyj ecga", "DisplayName": "DocAI System"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning", "Microsoft.AspNetCore.Hosting": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "./logs/log-.txt", "rollingInterval": "Day"}}]}, "AllowedHosts": "*", "GoogleOAuth": {"ClientId": "************-nupv073mfpk4aube45npcql8e6f4l559.apps.googleusercontent.com", "ClientSecret": "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", "RedirectUri": "https://production.docai.asia/api/auth/google/callback"}}