﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<!--<DocumentationFile>ChatBox.API.xml</DocumentationFile>-->
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Consumers\**" />
	  <Content Remove="Consumers\**" />
	  <EmbeddedResource Remove="Consumers\**" />
	  <None Remove="Consumers\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="MassTransit" Version="8.5.1" />
		<PackageReference Include="MassTransit.RabbitMQ" Version="8.5.1" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.7" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />
		<PackageReference Include="Microsoft.KernelMemory.AI.Tiktoken" Version="0.98.250508.3" />
		<PackageReference Include="Microsoft.ML.Tokenizers" Version="1.0.2" />
		<PackageReference Include="Microsoft.SemanticKernel" Version="1.61.0" />
		<PackageReference Include="Microsoft.SemanticKernel.Abstractions" Version="1.61.0" />
		<PackageReference Include="Microsoft.SemanticKernel.Connectors.OpenAI" Version="1.61.0" />
		<PackageReference Include="Microsoft.SemanticKernel.Core" Version="1.61.0" />
		<PackageReference Include="Polly" Version="8.6.1" />
		<PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
		<PackageReference Include="Serilog.Expressions" Version="5.1.0-dev-02301" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="SharpToken" Version="2.0.3" />
		<PackageReference Include="Tiktoken" Version="2.2.0" />
		<PackageReference Include="Tiktoken.Encodings.Abstractions" Version="2.2.0" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="..\..\..\.dockerignore">
			<Link>.dockerignore</Link>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\Shared\Shared.csproj" />
		<ProjectReference Include="..\ChatBox.Infrastructure\ChatBox.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Hubs\" />
	</ItemGroup>

</Project>
