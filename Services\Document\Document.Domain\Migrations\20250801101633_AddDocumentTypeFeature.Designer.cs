﻿// <auto-generated />
using System;
using Document.Domain.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Document.Domain.Migrations
{
    [DbContext(typeof(DocAIDocumentContext))]
    [Migration("20250801101633_AddDocumentTypeFeature")]
    partial class AddDocumentTypeFeature
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "vector");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Document.Domain.Model.Bookmark", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DocumentId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.ToTable("Bookmarks");
                });

            modelBuilder.Entity("Document.Domain.Model.DocumentFile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("DocumentTypeId")
                        .HasColumnType("text");

                    b.Property<bool>("IsReplaced")
                        .HasColumnType("boolean");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OwnerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReplacementDocumentId")
                        .HasColumnType("text");

                    b.Property<string>("ReplacementId")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentTypeId");

                    b.HasIndex("ReplacementDocumentId");

                    b.ToTable("DocumentFiles");
                });

            modelBuilder.Entity("Document.Domain.Models.ApprovalClaim", b =>
                {
                    b.Property<string>("DocumentVersionId")
                        .HasColumnType("text");

                    b.Property<DateTime>("ClaimedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ClaimedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("DocumentVersionId");

                    b.ToTable("ApprovalClaims");
                });

            modelBuilder.Entity("Document.Domain.Models.ApprovalLog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<string>("Comments")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DocumentVersionId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DocumentVersionId");

                    b.ToTable("ApprovalLogs");
                });

            modelBuilder.Entity("Document.Domain.Models.DocumentTag", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("DocumentVersionId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TagId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentVersionId");

                    b.HasIndex("TagId");

                    b.ToTable("DocumentTags");
                });

            modelBuilder.Entity("Document.Domain.Models.DocumentType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("DocumentTypes");
                });

            modelBuilder.Entity("Document.Domain.Models.DocumentVersion", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ApprovalClaimDocumentVersionId")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DocumentFileId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveUntil")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GoogleDriveFileId")
                        .HasColumnType("text");

                    b.Property<bool>("IsOfficial")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastSubmitted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SignedBy")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SubmittedBy")
                        .HasColumnType("text");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("TotalDownloads")
                        .HasColumnType("integer");

                    b.Property<string>("VersionName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ApprovalClaimDocumentVersionId");

                    b.HasIndex("DocumentFileId");

                    b.ToTable("DocumentVersions");
                });

            modelBuilder.Entity("Document.Domain.Models.Tag", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdatedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Tags");
                });

            modelBuilder.Entity("Document.Domain.Model.Bookmark", b =>
                {
                    b.HasOne("Document.Domain.Model.DocumentFile", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Document.Domain.Model.DocumentFile", b =>
                {
                    b.HasOne("Document.Domain.Models.DocumentType", "DocumentType")
                        .WithMany("DocumentFiles")
                        .HasForeignKey("DocumentTypeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Document.Domain.Model.DocumentFile", "ReplacementDocument")
                        .WithMany()
                        .HasForeignKey("ReplacementDocumentId");

                    b.Navigation("DocumentType");

                    b.Navigation("ReplacementDocument");
                });

            modelBuilder.Entity("Document.Domain.Models.ApprovalClaim", b =>
                {
                    b.HasOne("Document.Domain.Models.DocumentVersion", "DocumentVersion")
                        .WithOne()
                        .HasForeignKey("Document.Domain.Models.ApprovalClaim", "DocumentVersionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentVersion");
                });

            modelBuilder.Entity("Document.Domain.Models.ApprovalLog", b =>
                {
                    b.HasOne("Document.Domain.Models.DocumentVersion", "DocumentVersion")
                        .WithMany()
                        .HasForeignKey("DocumentVersionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentVersion");
                });

            modelBuilder.Entity("Document.Domain.Models.DocumentTag", b =>
                {
                    b.HasOne("Document.Domain.Models.DocumentVersion", "DocumentVersion")
                        .WithMany("DocumentTags")
                        .HasForeignKey("DocumentVersionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Document.Domain.Models.Tag", "Tag")
                        .WithMany("DocumentTags")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentVersion");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("Document.Domain.Models.DocumentVersion", b =>
                {
                    b.HasOne("Document.Domain.Models.ApprovalClaim", "ApprovalClaim")
                        .WithMany()
                        .HasForeignKey("ApprovalClaimDocumentVersionId");

                    b.HasOne("Document.Domain.Model.DocumentFile", "DocumentFile")
                        .WithMany("DocumentVersions")
                        .HasForeignKey("DocumentFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovalClaim");

                    b.Navigation("DocumentFile");
                });

            modelBuilder.Entity("Document.Domain.Model.DocumentFile", b =>
                {
                    b.Navigation("DocumentVersions");
                });

            modelBuilder.Entity("Document.Domain.Models.DocumentType", b =>
                {
                    b.Navigation("DocumentFiles");
                });

            modelBuilder.Entity("Document.Domain.Models.DocumentVersion", b =>
                {
                    b.Navigation("DocumentTags");
                });

            modelBuilder.Entity("Document.Domain.Models.Tag", b =>
                {
                    b.Navigation("DocumentTags");
                });
#pragma warning restore 612, 618
        }
    }
}
