<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="14.0.0" />
        <!-- Azure packages commented out for Google Drive migration -->
        <!-- <PackageReference Include="Azure.Identity" Version="1.14.0" /> -->
        <!-- <PackageReference Include="Azure.Storage.Blobs" Version="12.24.1" /> -->
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
        <PackageReference Include="Google.Apis.Drive.v3" Version="1.70.0.3834" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.KernelMemory.Abstractions" Version="0.98.250508.3" />
        <PackageReference Include="Microsoft.KernelMemory.AI.OpenAI" Version="0.98.250508.3" />
        <PackageReference Include="Microsoft.KernelMemory.Core" Version="0.98.250508.3" />
        <PackageReference Include="Microsoft.KernelMemory.MemoryDb.Postgres" Version="0.98.250508.3" />
        <PackageReference Include="Microsoft.KernelMemory.SemanticKernelPlugin" Version="0.98.250508.3" />
        <PackageReference Include="Microsoft.SemanticKernel.Connectors.Google" Version="1.60.0-alpha" />
        <PackageReference Include="MassTransit" Version="8.4.1" />
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.4.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />

        <PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
        <PackageReference Include="Pgvector" Version="0.3.2" />
        <PackageReference Include="Pgvector.EntityFrameworkCore" Version="0.2.2" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.2.1" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Expressions" Version="5.1.0-dev-02301" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
        <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.3" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\Shared\Shared.csproj" />
      <ProjectReference Include="..\Document.Infrastructure\Document.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>



</Project>
