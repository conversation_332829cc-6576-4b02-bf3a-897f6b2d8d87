{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning", "Microsoft.AspNetCore.Hosting": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "./logs/log-.txt", "rollingInterval": "Day"}}]}, "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=ChatbotDocsAI;Username=postgres;Password=**************", "Redis": "redis:6379,abortConnect=false"}, "AllowedHosts": "*", "OpenRouter": {"Endpoint": "https://openrouter.ai/api/v1", "Model": "mistralai/mistral-small-3.2-24b-instruct:free", "APIKey": "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"}, "Jwt": {"Secret": "DocAISystemSecretKeyForJWTTokenProNhuVayDoTaoDoThangNaoLayDuocKeyCuaTaoDo", "Issuer": "DocAI"}, "RabbitMQ": {"Host": "rabbitmq", "Username": "guest", "Password": "guest", "DocumentService": {"RequestTimeoutSeconds": 5}}}