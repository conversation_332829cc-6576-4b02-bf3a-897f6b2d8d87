apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-api
  labels:
    app: auth-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: auth-api
  template:
    metadata:
      labels:
        app: auth-api
    spec:
      containers:
        - name: auth-api
          image: magicflexing/docai-auth-api:latest
          ports:
            - containerPort: 5001
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
          livenessProbe:
            httpGet:
              path: /health
              port: 5001
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 5001
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: auth-api-service
spec:
  selector:
    app: auth-api
  ports:
    - protocol: TCP
      port: 5001
      targetPort: 5001
  type: ClusterIP
