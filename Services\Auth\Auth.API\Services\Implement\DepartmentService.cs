using System.Security.Authentication;
using Auth.API.Constants;
using Auth.API.Payload.Request.Department;
using Auth.API.Payload.Response;
using Auth.API.Payload.Response.Department;
using Auth.API.Services.Interface;
using Auth.Domain.Models;
using Auth.Infrastructure.Filter;
using Auth.Infrastructure.Paginate;
using Auth.Infrastructure.Repository.Interfaces;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace Auth.API.Services.Implement;

public class DepartmentService : BaseService<DepartmentService>, IDepartmentService
{
    private readonly IConfiguration _configuration;
    private readonly IRedisService _redisService;

    public DepartmentService(IUnitOfWork<DocAIAuthContext> unitOfWork, ILogger<DepartmentService> logger,
        IMapper mapper, IHttpContextAccessor httpContextAccessor, IConfiguration configuration,
        IRedisService redisService) : base(unitOfWork, logger, mapper, httpContextAccessor, configuration)
    {
        _redisService = redisService;
        _configuration = configuration;
    }

    public async Task<IPaginate<DepartmentResponse>> GetAllDepartmentsAsync(int page, int size,
        DepartmentFilter? filter, string? sortBy,
        bool isAsc)
    {
        var departments = await _unitOfWork.GetRepository<Department>().GetPagingListAsync(
            selector: s => new Department()
            {
                Id = s.Id,
                Name = s.Name,
                Description = s.Description,
                CreateAt = s.CreateAt,
                UpdateAt = s.UpdateAt,
            },
            page: page,
            size: size,
            filter: filter,
            sortBy: sortBy,
            isAsc: isAsc
        );
        var response = _mapper.Map<IPaginate<DepartmentResponse>>(departments);
        return response;
    }

    public async Task<DepartmentResponse> GetDepartmentInformationAsync(Guid departmentId)
    {
        if (departmentId == Guid.Empty)
            throw new AuthenticationException(MessageConstant.Department.DepartmentNotFound);
        var department = await _unitOfWork.GetRepository<Department>().SingleOrDefaultAsync(
            predicate: r => r.Id == departmentId
        );
        if (department == null)
            throw new BadHttpRequestException(MessageConstant.Department.DepartmentNotFound);
        var response = _mapper.Map<DepartmentResponse>(department);
        return response;
    }

    public async Task<DepartmentResponse> CreateDepartmentAsync(CreateDepartmentRequest request)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));
        var department = await _unitOfWork.GetRepository<Department>().SingleOrDefaultAsync(
            predicate: s => s.Name == request.DepartmentName
        );
        if (department != null)
            throw new BadHttpRequestException(MessageConstant.Department.DepartmentExist);
        var newDepartment = new Department()
        {
            Id = Guid.NewGuid(),
            Name = request.DepartmentName,
            Description = request.Description,
            CreateAt = DateTime.UtcNow,
            UpdateAt = DateTime.UtcNow,
        };
        await _unitOfWork.GetRepository<Department>().InsertAsync(newDepartment);
        var isSuccess = await _unitOfWork.CommitAsync() > 0;
        DepartmentResponse response = null;
        if (isSuccess) response = _mapper.Map<DepartmentResponse>(newDepartment);
        return response;
    }

    public async Task<DepartmentResponse> UpdateDepartmentAsync(UpdateDepartmentRequest request, Guid departmentId)
    {
        if (departmentId == Guid.Empty)
            throw new AuthenticationException(MessageConstant.Department.DepartmentNotFound);
        if (request == null)
            throw new AuthenticationException(MessageConstant.Department.DepartmentNotNull);
        var department = await _unitOfWork.GetRepository<Department>().SingleOrDefaultAsync(
            predicate: s => s.Id == departmentId
        );
        if (department == null)
            throw new BadHttpRequestException(MessageConstant.Department.DepartmentNotFound);
        department.Name = string.IsNullOrEmpty(request.DepartmentName) ? department.Name : request.DepartmentName;
        department.Description =
            string.IsNullOrEmpty(request.Description) ? department.Description : request.Description;
        department.UpdateAt = DateTime.UtcNow;
        _unitOfWork.GetRepository<Department>().UpdateAsync(department);
        var isSuccess = await _unitOfWork.CommitAsync() > 0;
        DepartmentResponse response = null;
        if (isSuccess) response = _mapper.Map<DepartmentResponse>(department);
        return response;
    }

    public async Task<DepartmentResponse> DeleteDepartmentAsync(Guid departmentId)
    {
        if (departmentId == Guid.Empty)
            throw new AuthenticationException(MessageConstant.Department.DepartmentNotFound);
        var department = await _unitOfWork.GetRepository<Department>().SingleOrDefaultAsync(
            predicate: s => s.Id == departmentId
        );
        if (department == null)
            throw new BadHttpRequestException(MessageConstant.Department.DepartmentNotFound);
        var departmentExist = await _unitOfWork.GetRepository<User>().SingleOrDefaultAsync(
            predicate: s => s.DepartmentId == departmentId,
            include: s => s.Include(s => s.Department)
            );
        if (departmentExist != null)
            throw new BadHttpRequestException(MessageConstant.Department.DeleteFailed);
        _unitOfWork.GetRepository<Department>().DeleteAsync(department);
        var isSuccess = await _unitOfWork.CommitAsync() > 0;
        DepartmentResponse response = null;
        if (isSuccess) response = _mapper.Map<DepartmentResponse>(department);
        return response;
    }

    public async Task<Dictionary<string, string>> GetDepartmentNamesByIdsAsync(List<string> departmentIds)
    {
        try
        {
            var result = new Dictionary<string, string>();
            
            if (!departmentIds.Any())
                return result;

            // Convert string IDs to Guids and filter valid ones
            var validGuids = new List<Guid>();
            foreach (var deptId in departmentIds)
            {
                if (Guid.TryParse(deptId, out var guid))
                {
                    validGuids.Add(guid);
                }
            }

            if (!validGuids.Any())
                return result;

            // Bulk query departments from database
            var departments = await _unitOfWork.GetRepository<Department>().GetListAsync(
                predicate: d => validGuids.Contains(d.Id),
                selector: d => new { d.Id, d.Name }
            );

            // Map results back to string IDs
            foreach (var dept in departments)
            {
                result[dept.Id.ToString()] = string.IsNullOrEmpty(dept.Name) ? "Unknown Department" : dept.Name;
            }

            _logger.LogInformation("Retrieved names for {Count} departments", result.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving department names by IDs");
            return new Dictionary<string, string>();
        }
    }
}