apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: docai-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-gateway-service
                port:
                  number: 80
          - path: /ai
            pathType: Prefix
            backend:
              service:
                name: ai-api-service
                port:
                  number: 5003
          - path: /auth
            pathType: Prefix
            backend:
              service:
                name: auth-api-service
                port:
                  number: 5001
          - path: /documents
            pathType: Prefix
            backend:
              service:
                name: document-api-service
                port:
                  number: 5002
          - path: /notifications
            pathType: Prefix
            backend:
              service:
                name: notification-api-service
                port:
                  number: 5004
