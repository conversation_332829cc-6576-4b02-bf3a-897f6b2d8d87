apiVersion: apps/v1
kind: Deployment
metadata:
  name: document-api
  labels:
    app: document-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: document-api
  template:
    metadata:
      labels:
        app: document-api
    spec:
      containers:
        - name: document-api
          image: magicflexing/docai-document-api:latest
          ports:
            - containerPort: 5002
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
          livenessProbe:
            httpGet:
              path: /health
              port: 5002
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 5002
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: document-api-service
spec:
  selector:
    app: document-api
  ports:
    - protocol: TCP
      port: 5002
      targetPort: 5002
  type: ClusterIP
