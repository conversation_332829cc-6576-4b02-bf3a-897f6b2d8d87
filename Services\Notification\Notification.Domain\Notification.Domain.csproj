﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
      <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="9.0.6" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
      <PackageReference Include="Quartz" Version="3.14.0" />
      <PackageReference Include="Quartz.AspNetCore" Version="3.14.0" />
    </ItemGroup>

</Project>
