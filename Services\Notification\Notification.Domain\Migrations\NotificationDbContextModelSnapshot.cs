﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Notification.Domain.Models;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Notification.Domain.Migrations
{
    [DbContext(typeof(NotificationDbContext))]
    partial class NotificationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Notification.Domain.Models.EmailTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AssociatedEvent")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("BodyHtml")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TemplateName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("TemplateName")
                        .IsUnique();

                    b.ToTable("EmailTemplates", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("a1b2c3d4-e5f6-7890-1234-567890abcdef"),
                            AssociatedEvent = "NearingExpiration",
                            BodyHtml = "<p>Dear User,</p><p>This is a reminder that the document <b>'{{DocumentTitle}}'</b> (version <b>{{DocumentVersion}}</b>) is scheduled to expire on <b>{{EffectiveUntil}}</b>.</p><p>Please review and take necessary action: <a href='{{DocumentLink}}'>View Document</a>.</p><hr><p><small>If you have already taken action, you can <a href='{{DismissLink}}'>dismiss future notifications for this version</a>.</small></p>",
                            CreateAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Subject = "[DocAI Reminder] Document '{{DocumentTitle}}' is Nearing Expiration",
                            TemplateName = "DocumentNearingExpiration",
                            UpdateAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("b2c3d4e5-f6a7-8901-2345-67890abcdef1"),
                            AssociatedEvent = "Expired",
                            BodyHtml = "<p>Dear User,</p><p>The document <b>'{{DocumentTitle}}'</b> (version <b>{{DocumentVersion}}</b>) expired on <b>{{EffectiveUntil}}</b> and is no longer active.</p><p>The document's status has been automatically updated to 'Expired'. Please review: <a href='{{DocumentLink}}'>View Document</a>.</p><hr><p><small>You can <a href='{{DismissLink}}'>dismiss any further related notifications for this version</a>.</small></p>",
                            CreateAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Subject = "[DocAI Alert] Document '{{DocumentTitle}}' Has Expired",
                            TemplateName = "DocumentExpired",
                            UpdateAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("Notification.Domain.Models.NotificationConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("LogRetentionDays")
                        .HasColumnType("integer");

                    b.Property<bool>("QuartzEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("ScanCronExpression")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("WarningThresholdDays")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ConfigKey")
                        .IsUnique();

                    b.ToTable("NotificationConfigs", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("c3d4e5f6-a7b8-9012-3456-7890abcdef12"),
                            ConfigKey = "Default",
                            CreateAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            LogRetentionDays = 90,
                            QuartzEnabled = true,
                            ScanCronExpression = "0 0 7 * * ?",
                            UpdateAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            WarningThresholdDays = 7
                        });
                });

            modelBuilder.Entity("Notification.Domain.Models.NotificationLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DismissToken")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DismissedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DismissedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("DocumentVersion")
                        .HasColumnType("text");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("IsDismissed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSent")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NotificationType")
                        .HasColumnType("integer");

                    b.Property<string>("RecipientAddress")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("RecipientType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreateAt");

                    b.HasIndex("DismissToken")
                        .IsUnique();

                    b.HasIndex("DocumentId");

                    b.HasIndex("IsSent");

                    b.HasIndex("NotificationType");

                    b.ToTable("NotificationLogs", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
