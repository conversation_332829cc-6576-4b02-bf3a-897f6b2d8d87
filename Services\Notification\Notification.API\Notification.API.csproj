<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="14.0.0" />
        <PackageReference Include="MassTransit" Version="8.4.1" />
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.4.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
        <PackageReference Include="Polly" Version="8.6.1" />
        <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.2.1" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Expressions" Version="5.1.0-dev-02301" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.41" />
        <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\Shared\Shared.csproj" />
      <ProjectReference Include="..\Notification.Infrastructure\Notification.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

</Project>
