{"version": "2.0.0", "tasks": [{"label": "build-apigateway", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/ApiGateway/ApiGateway.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-apphost", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/DocAI.AppHost/DocAI.AppHost.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-ai-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Services/AI/AI.API/AI.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-auth-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Services/Auth/Auth.API/Auth.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-document-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Services/Document/Document.API/Document.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-notification-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Services/Notification/Notification.API/Notification.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-chatbox-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Services/ChatBox/ChatBox.API/ChatBox.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-solution", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/DocAI_KingOfBE.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "clean-solution", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/DocAI_KingOfBE.sln"], "problemMatcher": "$msCompile"}, {"label": "restore-solution", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/DocAI_KingOfBE.sln"], "problemMatcher": "$msCompile"}]}