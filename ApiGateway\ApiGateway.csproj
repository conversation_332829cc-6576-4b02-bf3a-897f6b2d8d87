<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4"/>
        <PackageReference Include="Microsoft.OpenApi.Readers" Version="1.6.24" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.2.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="9.0.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="9.0.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.1" />
        <PackageReference Include="Yarp.ReverseProxy" Version="2.3.0" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

</Project>
