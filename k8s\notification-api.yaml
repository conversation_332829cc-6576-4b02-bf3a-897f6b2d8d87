apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-api
  labels:
    app: notification-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: notification-api
  template:
    metadata:
      labels:
        app: notification-api
    spec:
      containers:
        - name: notification-api
          image: magicflexing/docai-notification-api:latest
          ports:
            - containerPort: 5004
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
          livenessProbe:
            httpGet:
              path: /health
              port: 5004
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 5004
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: notification-api-service
spec:
  selector:
    app: notification-api
  ports:
    - protocol: TCP
      port: 5004
      targetPort: 5004
  type: ClusterIP
