{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "JWT": {
    "Secret": "DocAISystemSecretKeyForJWTTokenProNhuVayDoTaoDoThangNaoLayDuocKeyCuaTaoDo",
    "Issuer": "DocAI"
  },
  "RabbitMQ": {
    "Host": "rabbitmq",
    "Username": "guest",
    "Password": "guest"
  },
  "DocumentEnrichment": {
    "Enabled": true,
    "TimeoutSeconds": 2
  },
  "ConnectionStrings": {
    //"DefaultConnection": "Host=localhost;Port=5432;Database=DocAIDocumentData;Username=postgres;Password=*****",
    "DefaultConnection": "Host=**************;Port=5432;Database=DocAIDocumentTEST1;Username=postgres;Password=**************",
    "Redis": "redis:6379,abortConnect=false"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft.AspNetCore.Mvc": "Warning",
        "Microsoft.AspNetCore.Routing": "Warning",
        "Microsoft.AspNetCore.Hosting": "Warning",
        "Microsoft.KernelMemory": "Error",
        "Microsoft.KernelMemory.SemanticKernel": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "./logs/log-.txt",
          "rollingInterval": "Day"
        }
      }
    ]
  },
  "AllowedHosts": "*",
  "Ollama": {
    "Endpoint": "http://ollama:11434",
    //"Endpoint": "http://127.0.0.1:11434",
    "TextModel": "gemma3:1b",
    "EmbeddingModel": "nomic-embed-text:v1.5",
    "Temperature": 0.7,
    "TopP": 0.9,
    "NumPredict": 1024
  },
  "OpenRouter": {
    "Endpoint": "https://openrouter.ai/api/v1",
    "Model": "mistralai/mistral-small-3.1-24b-instruct:free",
    "APIKey": "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",
    "MaxTokens": 4000,
    "Temperature": 0.3
  },
  "AlternativeModels": {
    "Comment": "Alternative free models with higher token limits",
    "Options": [
      {
        "Model": "meta-llama/llama-3.2-3b-instruct:free",
        "MaxTokens": 2048,
        "Description": "LLaMA 3.2 3B - Free with 2048 output tokens"
      },
      {
        "Model": "google/gemma-2-9b-it:free",
        "MaxTokens": 8192,
        "Description": "Gemma 2 9B - Free with 8192 output tokens"
      },
      {
        "Model": "microsoft/phi-3-mini-128k-instruct:free",
        "MaxTokens": 4096,
        "Description": "Phi-3 Mini - Free with 4096 output tokens"
      }
    ]
  },
  "OpenAI": {
    "APIKey": "********************************************************************************************************************************************************************",
    "EmbeddingModel": "text-embedding-3-small"
  },
  // Azure Storage configuration commented out for Google Drive migration
  // "AzureStorage": {
  //   "BlobStorage": {
  //     "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=docsaivnstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  //     "ContainerName": "docsaivnstorage"
  //   }
  // },
  "GoogleDrive": {
    "CompanyAccountEmail": "<EMAIL>",
    "ClientId": "************-nupv073mfpk4aube45npcql8e6f4l559.apps.googleusercontent.com",
    "ClientSecret": "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",
    "CompanyRootFolderId": "",
    "ApplicationName": "DocAI Document Management",
    "BaseUrl": "https://production.docai.asia",
    "Scopes": [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/drive.file"
    ],
    "TimeoutSeconds": 300,
    "MaxRetryAttempts": 3,
    "BaseDelayMs": 1000,
    "EnableDetailedLogging": true,
    "UseCompanyAccountForWrites": true,
    "AutoShareWithDepartmentUsers": true,
    "FolderMapping": {
      "Drafts": "drafts",
      "Pending": "pending",
      "Approved": "approved",
      "Archived": "archived",
      "Public": "public"
    }
  },
  "Storage": {
    "UseGoogleDrive": true,
    "EnableFallback": false,
    "EnableMigrationMode": false
  }
}
